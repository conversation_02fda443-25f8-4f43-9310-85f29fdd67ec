﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class TaxesAndStocksSync : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "OzonTax",
                table: "Shop",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Tax",
                table: "Shop",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WildberriesTax",
                table: "Shop",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "YandexTax",
                table: "Shop",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OzonTax",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "Tax",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "WildberriesTax",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "YandexTax",
                table: "Shop");
        }
    }
}
