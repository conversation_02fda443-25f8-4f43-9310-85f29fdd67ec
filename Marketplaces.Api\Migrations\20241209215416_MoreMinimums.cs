﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class MoreMinimums : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Tax",
                table: "Shop",
                newName: "BankTax");

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumProfitPercentage",
                table: "YandexNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumRevenue",
                table: "YandexNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumProfitPercentage",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumRevenue",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumProfitPercentage",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumRevenue",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MinimumProfitPercentage",
                table: "YandexNomenclature");

            migrationBuilder.DropColumn(
                name: "MinimumRevenue",
                table: "YandexNomenclature");

            migrationBuilder.DropColumn(
                name: "MinimumProfitPercentage",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "MinimumRevenue",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "MinimumProfitPercentage",
                table: "OzonNomenclature");

            migrationBuilder.DropColumn(
                name: "MinimumRevenue",
                table: "OzonNomenclature");

            migrationBuilder.RenameColumn(
                name: "BankTax",
                table: "Shop",
                newName: "Tax");
        }
    }
}
