namespace Marketplaces.Api.Dtos.Wildberries;

public class WbFbsStockDto
{
    public DateTime LastChangeDate { get; set; }
    public string WarehouseName { get; set; } = null!;
    public string SupplierArticle { get; set; } = null!;
    public int NmId { get; set; }
    public string Barcode { get; set; } = null!;
    public int Quantity { get; set; }
    public int InWayToClient { get; set; }
    public int InWayFromClient { get; set; }
    public int QuantityFull { get; set; }
    public string Category { get; set; } = null!;
    public string Subject { get; set; } = null!;
    public string Brand { get; set; } = null!;
    public string TechSize { get; set; } = null!;
    public decimal Price { get; set; }
    public decimal Discount { get; set; }
    public bool IsSupply { get; set; }
    public bool IsRealization { get; set; }
    public string SCCode { get; set; } = null!;
}