﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class MoreDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "AcceptanceSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DeductionSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DeliverySum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PenaltiesSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "StorageFeeSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ToSendToSellerSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AcceptanceSum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "DeductionSum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "DeliverySum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "PenaltiesSum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "StorageFeeSum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "ToSendToSellerSum",
                table: "WildberriesNomenclature");
        }
    }
}
