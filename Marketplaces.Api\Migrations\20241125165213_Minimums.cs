﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class Minimums : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "MinimumProfitPercentage",
                table: "Shop",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumProfitPercentage",
                table: "Nomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumRevenue",
                table: "Nomenclature",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MinimumProfitPercentage",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "MinimumProfitPercentage",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "MinimumRevenue",
                table: "Nomenclature");
        }
    }
}
