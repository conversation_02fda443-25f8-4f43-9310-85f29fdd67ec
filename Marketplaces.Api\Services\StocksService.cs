using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Microsoft.EntityFrameworkCore;

namespace Marketplaces.Api.Services;

public class StocksService
{
    private readonly DatabaseContext _context;
    private readonly WildberriesClient _wbClient;
    private readonly YandexClient _yandexClient;
    private readonly OzonClient _ozonClient;
    private readonly ILogger<StocksService> _logger;

    public StocksService(DatabaseContext context, WildberriesClient wbClient, YandexClient yandexClient,
        OzonClient ozonClient, ILogger<StocksService> logger)
    {
        _context = context;
        _wbClient = wbClient;
        _yandexClient = yandexClient;
        _ozonClient = ozonClient;
        _logger = logger;
    }

    public async Task SyncStocks(string userId)
    {
        var shop = await _context.Shops.Include(s => s.ShopUsers)
            .FirstOrDefaultAsync(s => s.ShopUsers.Select(su => su.UserId).Contains(userId));

        if (shop is null)
        {
            throw new BadRequestException("Магазин не найден для пользователя");
        }

        var nomenclatures = await _context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .ToListAsync();

        List<Task> tasks =
        [
            SyncWbStocks(shop, nomenclatures),
            SyncOzonStocks(shop, nomenclatures),
            SyncYandexStocks(shop, nomenclatures),
            SyncFboStocksWorWb(shop, nomenclatures),
        ];

        await Task.WhenAll(tasks);
        await _context.SaveChangesAsync();
    }

    private async Task SyncYandexStocks(Shop shop, List<Nomenclature> nomenclatures)
    {
        var yandexAuthenticationData = shop.GetToken(Marketplace.Yandex);
        if (yandexAuthenticationData != null)
        {
            var yandexNomenclatures = nomenclatures
                .Where(n => n.ShopId == shop.Id && n.Yandex != null)
                .ToList();

            var yandexStockDtos = await _yandexClient.GetFBSStocks(yandexAuthenticationData, yandexNomenclatures
                .Select(wb => wb.Yandex?.Code).OfType<string>().ToList());

            foreach (var stockDto in yandexStockDtos)
            {
                var nomenclature = yandexNomenclatures.First(w => w.Yandex?.Code == stockDto.OfferId);
                nomenclature.UpdateYandexFbs(stockDto.Stocks.Find(s => s.Type == "AVAILABLE")?.Count ?? 0);
            }
        }
    }

    private async Task SyncOzonStocks(Shop shop, List<Nomenclature> nomenclatures)
    {
        if (!shop.ContainsToken(Marketplace.Ozon))
        {
            return;
        }

        var ozonNomenclatures = nomenclatures
            .Where(n => n.ShopId == shop.Id && n.Ozon != null)
            .Select(n => n.Ozon!)
            .ToList();

        var ozoFboStockDtos = await _ozonClient.GetStocks(shop, ozonNomenclatures
            .Select(ozon => ozon.Id).ToList());

        foreach (var ozonNomenclature in ozonNomenclatures)
        {
            var dto = ozoFboStockDtos.Find(w => w.ProductId == ozonNomenclature.Id);
            if (dto == null)
            {
                continue;
            }
            
            ozonNomenclature.UpdateFbo(dto.Stocks.Find(s => s.Type == "fbo")?.Present
                                       - dto.Stocks.Find(s => s.Type == "fbo")?.Reserved);
            ozonNomenclature.UpdateFbs(dto.Stocks.Find(s => s.Type == "fbs")?.Present
                                                - dto.Stocks.Find(s => s.Type == "fbs")?.Reserved);
        }
    }

    private async Task SyncWbStocks(Shop shop, List<Nomenclature> nomenclatures)
    {
        var wbAuthenticationData = shop.GetToken(Marketplace.Wildberries);
        if (wbAuthenticationData != null)
        {
            var wbNomenclatures = nomenclatures
                .Where(n => n.ShopId == shop.Id && n.Wildberries != null)
                .ToList();

            var wbWarehouses = await _wbClient.GetWarehouses(wbAuthenticationData);
            var one = wbWarehouses.FirstOrDefault(w => w.DeliveryType == 1);
            if (one == null)
            {
                return;
            }

            var wbStocksDtos = await _wbClient.GetStocks(wbAuthenticationData, one.Id, wbNomenclatures
                .Select(wb => wb.Wildberries?.SKU).OfType<string>().ToList());

            foreach (var stockDto in wbStocksDtos)
            {
                var nomenclature = wbNomenclatures.First(w => w.Wildberries?.SKU == stockDto.Sku);
                nomenclature.UpdateWildberriesFbs(stockDto.Amount);
            }
        }
    }

    public async Task SyncFboStocksWorWb(Shop shop, List<Nomenclature> nomenclatures)
    {
        var wbAuthenticationData = shop.GetToken(Marketplace.Wildberries);
        if (wbAuthenticationData != null)
        {
            var wbNomenclatures = nomenclatures
                .Where(n => n.ShopId == shop.Id && n.Wildberries != null)
                .ToList();

            try
            {
                var wbStocksDtos = await _wbClient.GetLastFboStocks(wbAuthenticationData, shop.Id);
                var aggregation = wbStocksDtos.GroupBy(w => w.Barcode).Select(w => new
                {
                    NmId = w.Key,
                    Amount = w.Any() ? w.Sum(s => s.Quantity) : 0
                });

                foreach (var stockDto in aggregation)
                {
                    var nomenclature = wbNomenclatures.FirstOrDefault(w => w.Wildberries?.SKU == stockDto.NmId);
                    nomenclature?.UpdateWildberriesFbo(stockDto.Amount);
                }
            }
            catch (Exception e)
            {
                // Console.WriteLine(e);
                _logger.LogWarning(e, "Exception in _wbClient.GetLastFboStocks");
            }
        }
    }
}