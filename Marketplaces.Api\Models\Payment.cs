using Marketplaces.Api.Exceptions;

namespace Marketplaces.Api.Models;

public class Payment
{
    public Payment(int shopId, int price)
    {
        ShopId = shopId;
        Price = price;
        CreatedAt = DateTimeOffset.UtcNow;
    }

    public Payment()
    {
    }
    
    public void Confirm()
    {
        if (ConfirmedAt != null)
        {
            throw new ApiException("Payment has already been confirmed.");
        }
        
        if (CanceledAt != null)
        {
            throw new ApiException("Payment confirmation has been canceled.");
        }
        
        ConfirmedAt = DateTimeOffset.UtcNow;
    }

    public void Cancel()
    {
        CanceledAt = DateTimeOffset.UtcNow;
    } 
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset? ConfirmedAt { get; set; }
    public DateTimeOffset? CanceledAt { get; set; }

    public Guid Id { get; set; }
    public int ShopId { get; set; }
    public Guid? ExternalId { get; set; }
    public DateTimeOffset? ExpiresAt { get; set; }
    public int Price { get; set; }

    public void Update(Guid externalId, DateTimeOffset expiresAt)
    {
        ExternalId = externalId;
        ExpiresAt = expiresAt;
    }
}