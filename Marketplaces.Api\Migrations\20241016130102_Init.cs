﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class Init : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Shop",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    OzonClientId = table.Column<string>(type: "text", nullable: true),
                    YandexClientId = table.Column<string>(type: "text", nullable: true),
                    OzonKey = table.Column<string>(type: "text", nullable: true),
                    WbKey = table.Column<string>(type: "text", nullable: true),
                    YandexKey = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Shop", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Nomenclature",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ShopId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Created = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Updated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Image = table.Column<string>(type: "text", nullable: true),
                    OzonId = table.Column<int>(type: "integer", nullable: true),
                    OzonCode = table.Column<string>(type: "text", nullable: true),
                    OzonSKU = table.Column<string>(type: "text", nullable: true),
                    WildberriesId = table.Column<int>(type: "integer", nullable: true),
                    WildberriesCode = table.Column<string>(type: "text", nullable: true),
                    WildberriesSKU = table.Column<string>(type: "text", nullable: true),
                    YandexId = table.Column<string>(type: "text", nullable: false),
                    YandexCode = table.Column<string>(type: "text", nullable: true),
                    YandexSKU = table.Column<string>(type: "text", nullable: true),
                    OzonFbs = table.Column<int>(type: "integer", nullable: true),
                    OzonFbo = table.Column<int>(type: "integer", nullable: true),
                    WildberriesFbs = table.Column<int>(type: "integer", nullable: true),
                    WildberriesFbo = table.Column<int>(type: "integer", nullable: true),
                    YandexFbs = table.Column<int>(type: "integer", nullable: true),
                    YandexFbo = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Nomenclature", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Nomenclature_Shop_ShopId",
                        column: x => x.ShopId,
                        principalTable: "Shop",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Nomenclature_ShopId",
                table: "Nomenclature",
                column: "ShopId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Nomenclature");

            migrationBuilder.DropTable(
                name: "Shop");
        }
    }
}
