using Marketplaces.Api.Exceptions;
using Microsoft.AspNetCore.Identity;

namespace Marketplaces.Api.Models.Identity;

public sealed class ApplicationUser : IdentityUser
{
    public ApplicationUser(string email, string firstName, string lastName, string? middleName, bool isConsentGiven,
        int? partnerId)
    {
        if (!isConsentGiven)
        {
            throw new BadRequestException("Нужно согласие");
        }

        Email = email;
        FirstName = firstName;
        LastName = lastName;
        MiddleName = middleName;
        IsConsentGiven = isConsentGiven;
        UserName = email;
        PartnerId = partnerId;
    }


    public ApplicationUser()
    {
    }

    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string? MiddleName { get; set; }
    public ICollection<ShopUser> ShopUsers { get; set; } = [];
    public bool IsConsentGiven { get; set; }
    public int? PartnerId { get; set; }

    public void UpdateInfo(string firstName, string lastName, string? middleName)
    {
        FirstName = firstName;
        LastName = lastName;
        MiddleName = middleName;
    }
}

public class ShopUser
{
    public ShopUser()
    {
    }

    public int ShopId { get; set; }
    public Shop Shop { get; set; } = null!;

    public string UserId { get; set; } = null!;
    public ApplicationUser User { get; set; } = null!;
}