using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Marketplaces.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Authorize(Policy = Policies.ActiveSubscription)]
[Route("shops/current/stocks")]
public class StocksController : ShopAbstractController
{
    private readonly DatabaseContext _context;
    private readonly IMapper _mapper;
    private readonly WildberriesClient _wbClient;
    private readonly OzonClient _ozonClient;
    private readonly YandexClient _yandexClient;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly StocksService _stocksService;

    public StocksController(DatabaseContext context,
        IMapper mapper,
        WildberriesClient wbClient,
        OzonClient ozonClient,
        YandexClient yandexClient,
        UserManager<ApplicationUser> userManager, StocksService stocksService) : base(context)
    {
        _context = context;
        _mapper = mapper;
        _wbClient = wbClient;
        _ozonClient = ozonClient;
        _yandexClient = yandexClient;
        _userManager = userManager;
        _stocksService = stocksService;
    }


    [HttpPost("sync")]
    public async Task<IActionResult> SyncStocks()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        await _stocksService.SyncStocks(user.Id);
        return Ok();
    }

    [HttpPut("{nomenclatureId}")]
    public async Task<IActionResult> UpdateStocks(int nomenclatureId, [FromBody] UpdateStocksBody body)
    {
        var shop = await GetShop();
        var nomenclature = await _context.FullNomenclatures
            .FirstOrDefaultAsync(n => n.ShopId == shop.Id && n.Id == nomenclatureId);
        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        if (body.Marketplace is null or Marketplace.Ozon && nomenclature.Ozon != null)
        {
            nomenclature.Ozon.UpdateFbs(body.Amount);
            var warehouses = await _ozonClient.GetWarehouseIds(shop);
            await _ozonClient.UpdateStocks(shop, warehouses[0], nomenclature.Ozon.Code, body.Amount);
        }

        if (body.Marketplace is null or Marketplace.Wildberries && nomenclature.Wildberries != null)
        {
            var token = shop.GetToken(Marketplace.Wildberries);
            var warehouses = await _wbClient.GetWarehouses(token);
            var one = warehouses.FirstOrDefault(w => w.DeliveryType == 1);
            if (one is not null)
            {
                await _wbClient.UpdateStocks(token, one.Id, nomenclature.Wildberries.Code,
                    nomenclature.Wildberries.SKU, body.Amount);
                nomenclature.UpdateWildberriesFbs(body.Amount);
            }

        }

        if (body.Marketplace is null or Marketplace.Yandex && nomenclature.Yandex != null)
        {
            nomenclature.UpdateYandexFbs(body.Amount);
            var token = shop.GetToken(Marketplace.Yandex);
            await _yandexClient.UpdateStocks(token, nomenclature.Yandex.Code, nomenclature.Yandex.SKU, body.Amount);
        }

        await _context.SaveChangesAsync();
        var stocksDto = _mapper.Map<StockDto>(nomenclature);
        return Ok(stocksDto);
    }

    [HttpGet]
    public async Task<IActionResult> GetStocks()
    {
        await SyncStocks();
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        var shop = await _context.Shops.Include(s => s.ShopUsers)
            .FirstOrDefaultAsync(s => s.ShopUsers.Select(su => su.UserId).Contains(user.Id));

        if (shop is null)
        {
            throw new NotFoundException();
        }

        var nomenclatures = await _context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        nomenclatures.LeaveOnlyWithTokens(shop);
        var stocks = _mapper.Map<List<StockDto>>(nomenclatures.SortByWierdRules());
        return Ok(stocks);
    }
}