namespace Marketplaces.Api.Responses;

public class InfoShopDto
{
    public decimal? OzonTax { get; set; }
    public decimal? OzonEstimatedTax { get; set; }
    public decimal? OzonMode { get; set; }
    public decimal? WildberriesTax { get; set; }
    public decimal? WildberriesEstimatedTax { get; set; }
    public decimal? WildberriesMode { get; set; }
    public decimal? YandexTax { get; set; }
    public decimal? BankTax { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public DateTimeOffset? LastPriceSyncTime { get; set; }
    public bool IsTrackingEnabled { get; set; }
}