﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class Change2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "YandexClientId",
                table: "Shop"
            );
            
            migrationBuilder.AddColumn<int>(
                name: "YandexClientId",
                table: "Shop",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "YandexBusinessId",
                table: "Shop",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "YandexCampaignId",
                table: "Shop",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "YandexBusinessId",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "YandexCampaignId",
                table: "Shop");

            migrationBuilder.AlterColumn<string>(
                name: "YandexClientId",
                table: "Shop",
                type: "text",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);
        }
    }
}
