using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;

namespace Marketplaces.Api.Controllers;

public static class NomenclaturesExtensions
{
    public static void LeaveOnlyWithTokens(this List<Nomenclature> nomenclatures, Shop shop)
    {
        var isOzonEmpty = string.IsNullOrEmpty((shop.GetToken(Marketplace.Ozon) as OzonAuthenticationData)?.ApiKey);
        var isWbEmpty = string.IsNullOrEmpty((shop.GetToken(Marketplace.Wildberries) as WbAuthenticationData)?.Token);
        var isYandexEmpty = string.IsNullOrEmpty((shop.GetToken(Marketplace.Yandex) as YandexAuthenticationData)?.ApiKey);

        foreach (var nomenclature in nomenclatures)
        {
            if (isOzonEmpty)
            {
                nomenclature.ResetOzon();
            }

            if (isWbEmpty)
            {
                nomenclature.ResetWildberries();
            }

            if (isYandexEmpty)
            {
                nomenclature.ResetYandex();
            }
        }
        
    }
}