using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Ozon.Promotes;

public class PromoteItemDto
{
    public int Id { get; set; }
    public string Title { get; set; } = null!;
    [JsonPropertyName("date_start")]
    public DateTime DateStart { get; set; }
    [JsonPropertyName("date_end")]
    public DateTime DateEnd { get; set; }
    [JsonPropertyName("potential_products_count")]
    public int PotentialProductsCount { get; set; }
    [JsonPropertyName("is_participating")]
    public bool IsParticipating { get; set; }
    [JsonPropertyName("participating_products_count")]
    public int ParticipatingProductsCount { get; set; }
    public string? Description { get; set; }
    [JsonPropertyName("action_type")]
    public string? ActionType { get; set; }
    [JsonPropertyName("banned_products_count")]
    public int BannedProductsCount { get; set; }
    [JsonPropertyName("with_targeting")]
    public bool WithTargeting { get; set; }
    [JsonPropertyName("discount_type")]
    public string? DiscountType { get; set; }
    [JsonPropertyName("discount_value")]
    public int DiscountValue { get; set; }
    [JsonPropertyName("order_amount")]
    public int OrderAmount { get; set; }
    [JsonPropertyName("freeze_date")]
    public string? FreezeDate { get; set; }
    [JsonPropertyName("is_voucher_action")]
    public bool IsVoucherAction { get; set; }
}