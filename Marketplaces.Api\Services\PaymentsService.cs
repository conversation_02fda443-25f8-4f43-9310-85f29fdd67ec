using Marketplaces.Api.Clients;
using Marketplaces.Api.Controllers;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using static System.Data.IsolationLevel;

namespace Marketplaces.Api.Services;

public class PaymentsService
{
    private readonly YookassaClient _yookassaClient;
    private readonly DatabaseContext _databaseContext;
    private readonly IMemoryCache _memoryCache;

    public PaymentsService(YookassaClient yookassaClient, DatabaseContext databaseContext, IMemoryCache memoryCache)
    {
        _yookassaClient = yookassaClient;
        _databaseContext = databaseContext;
        _memoryCache = memoryCache;
    }

    public async Task ProcessPayments(List<Guid> externalIds)
    {
        foreach (var externalId in externalIds)
        {
            var paymentDto = await _yookassaClient.GetPayment(externalId);
            switch (paymentDto?.Status)
            {
                case "succeeded":
                    await CreateOrUpdateSubscription(externalId);
                    break;
                case "canceled":
                    await CancelPayment(externalId);
                    break;
                case null:
                    break;
            }
        }
    }

    private async Task CancelPayment(Guid externalId)
    {
        var payment = await _databaseContext.Payments
            .FirstOrDefaultAsync(p => p.ExternalId == externalId);

        payment?.Cancel();
        await _databaseContext.SaveChangesAsync();
    }

    private async Task CreateOrUpdateSubscription(Guid externalId)
    {
        await using var transaction = await _databaseContext.Database.BeginTransactionAsync(Serializable);
        var aggregation = await _databaseContext.Shops
            .Include(s => s.Subscriptions)
            .Join(_databaseContext.Set<Payment>(),
                s => s.Id,
                p => p.ShopId,
                (s, p) => new { Shop = s, Payment = p })
            .FirstOrDefaultAsync(s => s.Payment.ExternalId == externalId);

        if (aggregation is null)
        {
            throw new ApiException("Shop not found during attempt to create subscription");
        }

        var shop = aggregation.Shop;
        var payment = aggregation.Payment;
        
        payment.Confirm();
        shop.CrateOrProlongSubscription(DateTimeOffset.Now.AddMonths(1));
        
        _databaseContext.Update(shop);
        _databaseContext.Update(payment);
        
        await _databaseContext.SaveChangesAsync();
        await transaction.CommitAsync();
        
        var users = await _databaseContext.ShopUsers
            .Include(su => su.User)
            .Where(su => su.ShopId == shop.Id)
            .Select(su => su.User)
            .ToListAsync();
        
        users.ForEach(u => _memoryCache.Remove($"userId#{u.Id}"));
    }
}