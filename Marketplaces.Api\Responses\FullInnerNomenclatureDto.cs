using System.Text.Json.Serialization;

namespace Marketplaces.Api.Responses;

public class FullInnerNomenclatureDto
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? BasePrice { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? Price { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? MarketingPrice { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? MarketingSellerPrice { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? MinPrice { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? OldPrice { get; set; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? DiscountPercent { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public string? Size { get; set; }
    public decimal? RedemptionPercent { get; set; }
    public decimal? EstimatedTax { get; set; }
    public string? Sku { get; set; }
    public string? Code { get; set; }
    public int? FbsAmount { get; set; }
    public int? FboAmount { get; set; }
}