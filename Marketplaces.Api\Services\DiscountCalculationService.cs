using Marketplaces.Api.Models;

namespace Marketplaces.Api.Services;

public class DiscountCalculationService
{
    /// <summary>
    /// Рассчитывает оптимальный процент скидки на основе минимальной прибыли и минимального процента прибыли
    /// </summary>
    /// <param name="basePrice">Основная цена товара</param>
    /// <param name="purchasePrice">Закупочная цена</param>
    /// <param name="minimumRevenue">Минимальная сумма прибыли</param>
    /// <param name="minimumProfitPercentage">Минимальный процент прибыли</param>
    /// <param name="bankTax">Комиссия банка в процентах</param>
    /// <param name="marketplaceTax">Комиссия маркетплейса в процентах</param>
    /// <returns>Рассчитанный процент скидки</returns>
    public (decimal calculatedDiscount, decimal discountForMinProfit, decimal discountForMinProfitPercentage, string details)
        CalculateOptimalDiscount(
            decimal basePrice,
            decimal purchasePrice,
            decimal minimumRevenue,
            decimal minimumProfitPercentage,
            decimal bankTax,
            decimal marketplaceTax)
    {
        // Общая доля комиссии (dK) = 1 - комиссия банка - комиссия маркетплейса
        var totalCommissionRate = (bankTax + marketplaceTax) / 100;
        var netRate = 1 - totalCommissionRate; // dK

        // 1) Расчет скидки для минимальной суммы прибыли
        // Формула: Основная цена * X * dK - Закупка = минимальная сумма прибыли
        // X = (Минимальная прибыль + Закупочная цена) / (Основная цена * dK)
        var xForMinRevenue = (minimumRevenue + purchasePrice) / (basePrice * netRate);
        var discountForMinRevenue = Math.Max(0, (1 - xForMinRevenue) * 100);

        // 2) Расчет скидки для минимального процента прибыли
        // Формула: (Основная цена * X * dK - Закупка) / Закупка = минимальный процент прибыли
        // X = Закупочная цена * (1 + минимальный процент прибыли) / (Основная цена * dK)
        var minProfitPercentageDecimal = minimumProfitPercentage / 100;
        var xForMinProfitPercentage = purchasePrice * (1 + minProfitPercentageDecimal) / (basePrice * netRate);
        var discountForMinProfitPercentage = Math.Max(0, (1 - xForMinProfitPercentage) * 100);

        // Выбираем меньшую скидку (более строгое условие)
        var finalDiscount = Math.Min(discountForMinRevenue, discountForMinProfitPercentage);

        // Округляем вниз до целого числа
        finalDiscount = Math.Floor(finalDiscount);
        discountForMinRevenue = Math.Floor(discountForMinRevenue);
        discountForMinProfitPercentage = Math.Floor(discountForMinProfitPercentage);

        var details = $"Расчет скидки:\n" +
                     $"Общая доля комиссии (dK): {netRate:F3}\n" +
                     $"Скидка для мин. прибыли {minimumRevenue} руб.: {discountForMinRevenue}%\n" +
                     $"Скидка для мин. процента прибыли {minimumProfitPercentage}%: {discountForMinProfitPercentage}%\n" +
                     $"Итоговая скидка (меньшая): {finalDiscount}%";

        return (finalDiscount, discountForMinRevenue, discountForMinProfitPercentage, details);
    }
}
