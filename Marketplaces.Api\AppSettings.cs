using System.ComponentModel.DataAnnotations;

namespace Marketplaces.Api;

public class AppSettings
{
    public string OzonUrl { get; set; } = "https://api-seller.ozon.ru/";
    public string YandexUrl { get; set; } = "https://api.partner.market.yandex.ru/";
    public string YookassaBaseUrl { get; set; } = "https://api.yookassa.ru/v3/";
    [Required]
    public string TokenSecret { get; set; } = null!;
    public string SmtpUrl { get; set; } = "smtp.timeweb.ru";
    [Required]
    public string EmailPassword { get; set; } = null!;
    public string ApplicationUrl { get; set; } = null!;
    public string DefaultEmail { get; set; } = "<EMAIL>";
    public bool IsTrackingEnabled { get; set; }
    public string YookassaShopId { get; set; } = null!;
    public string YookassaSecretKey { get; set; } = null!;
    public string YookassaReturnUrl { get; set; } = null!;
    public int TokenLifetimeInDays { get; set; }
}