using System.Text.Json;

namespace Marketplaces.Api.Helpers;

public static class <PERSON>H<PERSON>per
{
    public static void Patch<T>(this T obj, JsonElement patch)
    {
        var type = typeof(T);
        var properties = type.GetProperties();

        foreach (var property in properties)
        {
            var patchProperty = patch.EnumerateObject()
                .FirstOrDefault(x => x.Name.Equals(property.Name, StringComparison.OrdinalIgnoreCase));
            if (patchProperty.Value.ValueKind != JsonValueKind.Undefined &&
                TryParse(patchProperty.Value, property.PropertyType, out var result))
            {
                property.SetValue(obj, result);
            }
        }

        bool TryParse(JsonElement value, Type objectType, out object? result)
        {
            try
            {
                result = value.Deserialize(objectType, new JsonSerializerOptions(JsonSerializerDefaults.Web));
                return true;
            }
            catch
            {
                result = null;
                return false;
            }
        }
    }
}