using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Authorize]
[Route("feedback")]
public class FeedbackController: ControllerBase
{
    private readonly IEmailSender _emailSender;
    private readonly AppSettings _appSettings;
    private readonly UserManager<ApplicationUser> _userManager;

    public FeedbackController(IEmailSender emailSender, IOptions<AppSettings> options, UserManager<ApplicationUser> userManager)
    {
        _emailSender = emailSender;
        _userManager = userManager;
        _appSettings = options.Value;
    }

    [HttpPost]
    public async Task<IActionResult> CreateFeedback(CreateFeedbackBody body)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            throw new UnauthorizedException();
        }
            
        var text = $"Сообщение от пользователя ({user.Email}): {body.Message}";

        await _emailSender.SendEmailAsync(_appSettings.DefaultEmail, "Обратная связь", text);
        return Ok();
    }
}