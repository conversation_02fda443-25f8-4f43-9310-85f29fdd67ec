using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Yookassa;

public class PaymentDto
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("paid")]
    public bool Paid { get; set; }

    [JsonPropertyName("amount")]
    public Amount Amount { get; set; }

    [JsonPropertyName("confirmation")]
    public Confirmation Confirmation { get; set; }

    [JsonPropertyName("created_at")]
    public DateTimeOffset CreatedAt { get; set; }
    
    [JsonPropertyName("expires_at")]
    public DateTimeOffset ExpiresAt { get; set; }

    [Json<PERSON>ropertyName("description")]
    public string Description { get; set; }

    [JsonPropertyName("metadata")]
    public MetadataObject? Metadata { get; set; }

    [Json<PERSON>ropertyName("recipient")]
    public Recipient Recipient { get; set; }

    [Json<PERSON>ropertyName("refundable")]
    public bool Refundable { get; set; }

    [Json<PERSON>ropertyName("test")]
    public bool Test { get; set; }
}