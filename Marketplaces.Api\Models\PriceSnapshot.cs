using System.ComponentModel.DataAnnotations.Schema;

namespace Marketplaces.Api.Models;

public class PriceSnapshot
{
    public PriceSnapshot(int shopId, string data)
    {
        ShopId = shopId;
        CreatedAt = DateTimeOffset.UtcNow;
        Data = data;
    }

    private PriceSnapshot()
    {
    }

    public int ShopId { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    [Column(TypeName = "jsonb")]
    public string? Data { get; set; }

    public void Update(string data)
    {
        Data = data;
        CreatedAt = DateTimeOffset.UtcNow;
    }
}