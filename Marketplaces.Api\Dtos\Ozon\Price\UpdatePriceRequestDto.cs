using System.Globalization;
using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Ozon.Price;

public class UpdatePriceRequestDto
{
    public UpdatePriceRequestDto(string offerId, decimal price)
    {
        MinPrice = (price * 0.9m).ToString(CultureInfo.InvariantCulture);
        Price = price.ToString(CultureInfo.InvariantCulture);
        OldPrice = (price * 1.43m).ToString(CultureInfo.InvariantCulture);
        OfferId = offerId;
    }
    
    [JsonPropertyName("old_price")]
    public string OldPrice { get; set; }
    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; }
    [JsonPropertyName("min_price")]
    public string MinPrice { get; set; }
    public string Price { get; set; }
    [JsonPropertyName("currency_code")]
    public string CurrencyCode { get; set; } = "RUB";
    [JsonPropertyName("auto_action_enabled")]
    public string AutoActionEnabled { get; set; } ="UNKNOWN";
    [JsonPropertyName("price_strategy_enabled")]
    public string PriceStrategyEnabled { get; set; } = "UNKNOWN"!;
}