namespace Marketplaces.Api.Dtos.Internal;

public class WildberriesPriceInfoDto
{
    public decimal Price { get; set; }
    public decimal ToSendToSeller { get; set; }
    public decimal DeliveryCost { get; set; }
    public decimal Deduction { get; set; }
    public decimal Acceptance { get; set; }
    public decimal StorageFee { get; set; }
    public decimal Penalties { get; set; }
    public decimal RetailCost { get; set; }
    // На самом деле здесь количество доставок всех
    public int? RedemptionAmount { get; set; }
    public int? ReturnAmount { get; set; }
}