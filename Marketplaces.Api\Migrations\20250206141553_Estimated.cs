﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class Estimated : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "EstimatedTax",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "RedemptionPercent",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "RetailSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WithoutCommissionSum",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "LastOzonEstimation",
                table: "Shop",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "LastWildberriesEstimation",
                table: "Shop",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "OzonEstimatedTax",
                table: "Shop",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OzonMode",
                table: "Shop",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "WildberriesEstimatedTax",
                table: "Shop",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WildberriesMode",
                table: "Shop",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "EstimatedTax",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "RedemptionPercent",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EstimatedTax",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "RedemptionPercent",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "RetailSum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "WithoutCommissionSum",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "LastOzonEstimation",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "LastWildberriesEstimation",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "OzonEstimatedTax",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "OzonMode",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "WildberriesEstimatedTax",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "WildberriesMode",
                table: "Shop");

            migrationBuilder.DropColumn(
                name: "EstimatedTax",
                table: "OzonNomenclature");

            migrationBuilder.DropColumn(
                name: "RedemptionPercent",
                table: "OzonNomenclature");
        }
    }
}
