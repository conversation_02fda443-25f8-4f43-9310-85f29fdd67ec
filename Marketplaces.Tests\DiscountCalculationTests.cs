using Marketplaces.Api.Services;

namespace Marketplaces.Tests;

public class DiscountCalculationTests
{
    private readonly DiscountCalculationService _service = new();

    [Fact]
    public void CalculateOptimalDiscount_WithGivenExample_ShouldCalculateCorrectly()
    {
        // Arrange - данные из примера
        var basePrice = 650m;
        var purchasePrice = 44.2m;
        var minimumRevenue = 100m;
        var minimumProfitPercentage = 95m;
        var bankTax = 7m;
        var marketplaceTax = 35m;

        // Act
        var (calculatedDiscount, discountForMinProfit, discountForMinProfitPercentage, details) = 
            _service.CalculateOptimalDiscount(
                basePrice, 
                purchasePrice, 
                minimumRevenue, 
                minimumProfitPercentage, 
                bankTax, 
                marketplaceTax);

        // Assert
        // Общая доля комиссии (dK) = 1 - 0.07 - 0.35 = 0.58
        var expectedNetRate = 0.58m;
        
        // 1) Скидка для минимальной прибыли 100 руб
        // X = (100 + 44.2) / (650 * 0.58) = 144.2 / 377 = 0.382
        // Скидка = (1 - 0.382) * 100 = 61.8% -> округляем вниз до 61%
        Assert.Equal(61m, discountForMinProfit);
        
        // 2) Скидка для минимального процента прибыли 95%
        // X = 44.2 * (1 + 0.95) / (650 * 0.58) = 86.19 / 377 = 0.2286
        // Скидка = (1 - 0.2286) * 100 = 77.14% -> округляем вниз до 77%
        Assert.Equal(77m, discountForMinProfitPercentage);
        
        // Итоговая скидка - меньшая из двух
        Assert.Equal(61m, calculatedDiscount);
        
        // Проверяем, что детали содержат ожидаемую информацию
        Assert.Contains("0.580", details);
        Assert.Contains("61%", details);
        Assert.Contains("77%", details);
    }

    [Fact]
    public void CalculateOptimalDiscount_WithDifferentCommissions_ShouldCalculateCorrectly()
    {
        // Arrange
        var basePrice = 1000m;
        var purchasePrice = 100m;
        var minimumRevenue = 200m;
        var minimumProfitPercentage = 50m;
        var bankTax = 5m;
        var marketplaceTax = 25m;

        // Act
        var (calculatedDiscount, discountForMinProfit, discountForMinProfitPercentage, details) = 
            _service.CalculateOptimalDiscount(
                basePrice, 
                purchasePrice, 
                minimumRevenue, 
                minimumProfitPercentage, 
                bankTax, 
                marketplaceTax);

        // Assert
        // Общая доля комиссии (dK) = 1 - 0.05 - 0.25 = 0.70
        var expectedNetRate = 0.70m;
        
        // 1) Скидка для минимальной прибыли 200 руб
        // X = (200 + 100) / (1000 * 0.70) = 300 / 700 = 0.4286
        // Скидка = (1 - 0.4286) * 100 = 57.14% -> округляем вниз до 57%
        Assert.Equal(57m, discountForMinProfit);
        
        // 2) Скидка для минимального процента прибыли 50%
        // X = 100 * (1 + 0.50) / (1000 * 0.70) = 150 / 700 = 0.2143
        // Скидка = (1 - 0.2143) * 100 = 78.57% -> округляем вниз до 78%
        Assert.Equal(78m, discountForMinProfitPercentage);
        
        // Итоговая скидка - меньшая из двух
        Assert.Equal(57m, calculatedDiscount);
    }

    [Fact]
    public void CalculateOptimalDiscount_WhenNegativeDiscount_ShouldReturnZero()
    {
        // Arrange - случай когда даже без скидки условия не выполняются
        var basePrice = 100m;
        var purchasePrice = 200m; // Закупка больше базовой цены
        var minimumRevenue = 50m;
        var minimumProfitPercentage = 10m;
        var bankTax = 7m;
        var marketplaceTax = 35m;

        // Act
        var (calculatedDiscount, discountForMinProfit, discountForMinProfitPercentage, details) = 
            _service.CalculateOptimalDiscount(
                basePrice, 
                purchasePrice, 
                minimumRevenue, 
                minimumProfitPercentage, 
                bankTax, 
                marketplaceTax);

        // Assert - скидки не могут быть отрицательными
        Assert.True(calculatedDiscount >= 0);
        Assert.True(discountForMinProfit >= 0);
        Assert.True(discountForMinProfitPercentage >= 0);
    }
}
