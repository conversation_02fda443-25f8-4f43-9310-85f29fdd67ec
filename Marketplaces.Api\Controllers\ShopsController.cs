using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Marketplaces.Api.Controllers;

[Authorize(Policy = Policies.ActiveSubscription)]
[ApiController]
[Route("shops")]
public class ShopsController : ShopAbstractController
{
    private readonly DatabaseContext _context;
    private readonly IMapper _mapper;
    private readonly YandexClient _yandexClient;
    private readonly ILogger<ShopsController> _logger;

    public ShopsController(DatabaseContext context, IMapper mapper, YandexClient yandexClient, 
        ILogger<ShopsController> logger) : base(context)
    {
        _context = context;
        _mapper = mapper;
        _yandexClient = yandexClient;
        _logger = logger;
    }

    [HttpGet("current")]
    public async Task<IActionResult> GetShops()
    {
        var shop = await GetShop();
        var dtos = _mapper.Map<ShopDto>(shop);
        return Ok(dtos);
    }

    [HttpPut("current/token")]
    public async Task<IActionResult> UpdateToken(UpdateShopTokenBody body)
    {
        var shop = await GetShop();

        shop.UpdateToken(body.ClientId, body.Token, body.Marketplace);
        if (body.Marketplace == Marketplace.Yandex)
        {
            if (body.Token is null)
            {
                shop.ResetYandexIds();
            }
            else
            {
                _logger.LogInformation(body.Token ?? "Пусто");
                var yandexData = await _yandexClient.GetData(new YandexAuthenticationData() { ApiKey = body.Token! });
                shop.UpdateYandex(yandexData.Campaigns[0].ClientId, yandexData.Campaigns[0].Business.Id,
                    yandexData.Campaigns[0].Id);
            }
        }

        await _context.SaveChangesAsync();
        var dto = _mapper.Map<ShopDto>(shop);
        return Ok(dto);
    }
}