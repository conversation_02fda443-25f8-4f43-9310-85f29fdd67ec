on:
  push:
    branches:
      - testing
env:
  DOCKER_BUILDKIT: 1
jobs:
  publish:
    name: Publish
    runs-on: [internal, self-hosted]
    container: docker:stable
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Build container
        run: >
          docker build
          -t marketplaces-test-image:latest
          --target runtime
          .
      - name: Remove existing container
        run: |
          docker container stop marketplaces-test || true
          docker container rm marketplaces-test || true
      - name: Run app
        run: >
          docker run -d --restart always --name marketplaces-test -p 8082:8080/tcp
          -e ConnectionStrings__Postgres="${{ secrets.POSTGRES_MARKETPLACES_TEST }}"
          -e EmailPassword="${{ secrets.EMAIL_PASSWORD }}"
          -e YookassaShopId="${{ secrets.YOOKASSA_SHOP_ID_TEST }}"
          -e YookassaSecretKey="${{ secrets.YOOKASSA_SECRET_KEY_TEST }}"
          -e ASPNETCORE_ENVIRONMENT="Testing"
          marketplaces-test-image:latest
      - name: Prune images
        run: docker image prune -a -f
