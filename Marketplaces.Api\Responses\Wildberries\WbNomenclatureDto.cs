using System.Text.Json.Serialization;

namespace Marketplaces.Api.Responses.Wildberries;


public class WbCardsDto
{
    public List<WbNomenclatureDto> Cards { get; set; } = [];
    public WbCursorDto Cursor { get; set; } = new WbCursorDto();
}

public class WbNomenclatureDto
{
    [JsonPropertyName("nmID")] 
    public int Id { get; set; } // Артикул WB
    // public int imtID { get; set; } // Идентификатор КТ. Артикулы WB из одной КТ будут иметь одинаковый imtID
    // public Guid nmUUID { get; set; } // Внутренний технический идентификатор товара
    // public int subjectID { get; set; } // Идентификатор предмета
    [JsonPropertyName("vendorCode")] 
    public string Code { get; set; } = null!;// Артикул продавца
    public string Title { get; set; } = null!; // Наименование товара
    public List<WbSizeDto> Sizes { get; set; } = [];
    public List<WbPhoto> Photos { get; set; } = [];
}

public class WbPhoto
{
    public string c246x328 { get; set; }
}

public class WbCursorDto
{
    public DateTime UpdatedAt { get; set; }
    public int NmID { get; set; }
    public int Total { get; set; }
}

public class WbSizeDto
{
    public int ChrtId { get; set; }
    public string TechId { get; set; }
    public string WbSize { get; set; }
    public List<string> Skus { get; set; }
}