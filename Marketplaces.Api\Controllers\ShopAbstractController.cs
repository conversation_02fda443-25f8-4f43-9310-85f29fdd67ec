using System.Security.Claims;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Marketplaces.Api.Controllers;

public abstract class ShopAbstractController : ControllerBase
{
    protected readonly DatabaseContext Context;

    protected ShopAbstractController(DatabaseContext context)
    {
        Context = context;
    }

    protected async Task<Shop> GetShop()
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        var shop = await Context.Shops
            .Include(s => s.ShopUsers)
            .ThenInclude(su => su.User)
            .Include(s => s.Subscriptions
                .Where(su => su.StartDate >= DateTimeOffset.UtcNow && su.EndDate <= DateTime.UtcNow))
            .FirstOrDefaultAsync(s => s.ShopUsers.Select(su => su.UserId).Contains(userId));
        if (shop is null)
        {
            throw new NotFoundException("Проблема при получении магазина");
        }

        return shop;
    }
}