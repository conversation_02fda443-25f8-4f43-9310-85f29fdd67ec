using Marketplaces.Api.Models;
using Swashbuckle.AspNetCore.Filters;

namespace Marketplaces.Api.Requests.Examples;

public class UpdateInfoBodyExample : IExamplesProvider<UpdateInfoBody>
{
    public UpdateInfoBody GetExamples()
    {
        return new UpdateInfoBody
        {
            BankTax = 1.23m,
            OzonTax = 2.34m,
            OzonMode = TaxMode.Estimated,
            WildberriesTax = 3.45m,
            WildberriesMode = TaxMode.Manual,
            YandexTax = 4.56m,
            MinimumRevenue = 21.23m,
            MinimumProfitPercentage = 0.12m,
            IsTrackingEnabled = true
        };
    }
}