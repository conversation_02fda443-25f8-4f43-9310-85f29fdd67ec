using Marketplaces.Api.Responses.Wildberries;

namespace Marketplaces.Api.Databases;

public class WildberriesNomenclature : MarketplaceNomenclature
{
    private WildberriesNomenclature()
    {
    }

    public WildberriesNomenclature(int id, string name, string code, string sku, string russiaSize,
        List<WbPhoto> photos)
    {
        Id = id;
        Name = name;
        Code = code;
        SKU = sku;
        Size = russiaSize;
        UpdatePhoto(photos);
    }


    public string? Image { get; set; }
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string SKU { get; set; } = null!;
    public int? FBSAmount { get; set; }
    public int? FboAmount { get; set; }

    public Guid InternalId { get; set; }
    public decimal? Price { get; set; }
    public decimal? Discount { get; set; }
    
    public decimal? RedemptionPercent { get; set; }
    public decimal? EstimatedTax { get; set; }
    public decimal? WithoutCommissionSum { get; set; }
    public decimal? RetailSum { get; set; }
    public decimal? ToSendToSellerSum { get; set; }
    public decimal? DeliveryPriceSum { get; set; }
    public decimal? DeductionSum { get; set; }
    public decimal? AcceptanceSum { get; set; }
    public decimal? StorageFeeSum { get; set; }
    public decimal? PenaltiesSum { get; set; }
    public int? RedemptionAmount { get; set; }
    public int? ReturnAmount { get; set; }

    public void Update(int id, string code, string name, string sku)
    {
        Id = id;
        Name = name;
        Code = code;
        SKU = sku;
    }

    public void UpdatePhoto(List<WbPhoto> photos)
    {
        if (photos.Count > 0)
        {
            Image = photos[0].c246x328;
        }
    }

    public void UpdateFbs(int amount)
    {
        FBSAmount = amount;
    }

    public void UpdatePrice(decimal price)
    {
        Price = price;
    }

    public void UpdateDiscount(decimal? discount)
    {
        Discount = discount;
    }

    public void UpdateFbo(int amount)
    {
        FboAmount = amount;
    }

    public override void UpdateTitle(string name)
    {
        Name = name;
    }

    public void SetReportDetails(decimal retailSum, decimal withoutCommissionSum, decimal? commissionPercent,
        decimal? redemptionPercent)
    {
        RetailSum = retailSum;
        WithoutCommissionSum = withoutCommissionSum;
        EstimatedTax = commissionPercent;
        RedemptionPercent = redemptionPercent;
    }

    public void SetReportDetails(decimal retailSum, 
        decimal withoutCommissionSum, 
        decimal? commissionPercent, 
        decimal? redemptionPercent, 
        decimal toSendToSellerSum, 
        decimal deliveryPriceSum, 
        decimal deductionSum, 
        decimal acceptanceSum, 
        decimal storageFeeSum, 
        decimal penaltiesSum,
        int redemptionAmount,
        int returnAmount)
    {
        RetailSum = retailSum;
        WithoutCommissionSum = withoutCommissionSum;
        EstimatedTax = commissionPercent;
        RedemptionPercent = redemptionPercent;
        ToSendToSellerSum = toSendToSellerSum;
        DeliveryPriceSum = deliveryPriceSum;
        DeductionSum = deductionSum;
        AcceptanceSum = acceptanceSum;
        StorageFeeSum = storageFeeSum;
        PenaltiesSum = penaltiesSum;
        ReturnAmount = returnAmount;
        RedemptionAmount = redemptionAmount;
    }

}
