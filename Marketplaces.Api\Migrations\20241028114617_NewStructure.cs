﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class NewStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OzonCode",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "OzonFbo",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "OzonFbs",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "OzonId",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "OzonSKU",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "WildberriesCode",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "WildberriesFbo",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "WildberriesFbs",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "WildberriesId",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "WildberriesSKU",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "YandexCode",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "YandexFbo",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "YandexFbs",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "YandexId",
                table: "Nomenclature");

            migrationBuilder.DropColumn(
                name: "YandexSKU",
                table: "Nomenclature");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Nomenclature",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.CreateTable(
                name: "OzonNomenclature",
                columns: table => new
                {
                    InternalId = table.Column<Guid>(type: "uuid", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    SKU = table.Column<string>(type: "text", nullable: false),
                    NomenclatureId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OzonNomenclature", x => x.InternalId);
                    table.ForeignKey(
                        name: "FK_OzonNomenclature_Nomenclature_NomenclatureId",
                        column: x => x.NomenclatureId,
                        principalTable: "Nomenclature",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WildberriesNomenclature",
                columns: table => new
                {
                    InternalId = table.Column<Guid>(type: "uuid", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    SKU = table.Column<string>(type: "text", nullable: false),
                    NomenclatureId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WildberriesNomenclature", x => x.InternalId);
                    table.ForeignKey(
                        name: "FK_WildberriesNomenclature_Nomenclature_NomenclatureId",
                        column: x => x.NomenclatureId,
                        principalTable: "Nomenclature",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "YandexNomenclature",
                columns: table => new
                {
                    InternalId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Id = table.Column<string>(type: "text", nullable: true),
                    Code = table.Column<string>(type: "text", nullable: false),
                    SKU = table.Column<string>(type: "text", nullable: false),
                    NomenclatureId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_YandexNomenclature", x => x.InternalId);
                    table.ForeignKey(
                        name: "FK_YandexNomenclature_Nomenclature_NomenclatureId",
                        column: x => x.NomenclatureId,
                        principalTable: "Nomenclature",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OzonNomenclature_NomenclatureId",
                table: "OzonNomenclature",
                column: "NomenclatureId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WildberriesNomenclature_NomenclatureId",
                table: "WildberriesNomenclature",
                column: "NomenclatureId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_YandexNomenclature_NomenclatureId",
                table: "YandexNomenclature",
                column: "NomenclatureId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OzonNomenclature");

            migrationBuilder.DropTable(
                name: "WildberriesNomenclature");

            migrationBuilder.DropTable(
                name: "YandexNomenclature");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Nomenclature",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OzonCode",
                table: "Nomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OzonFbo",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OzonFbs",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OzonId",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OzonSKU",
                table: "Nomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WildberriesCode",
                table: "Nomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WildberriesFbo",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WildberriesFbs",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WildberriesId",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WildberriesSKU",
                table: "Nomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "YandexCode",
                table: "Nomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "YandexFbo",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "YandexFbs",
                table: "Nomenclature",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "YandexId",
                table: "Nomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "YandexSKU",
                table: "Nomenclature",
                type: "text",
                nullable: true);
        }
    }
}
