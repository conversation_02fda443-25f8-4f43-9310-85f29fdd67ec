using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Microsoft.Extensions.Options;
using RestSharp;

namespace Marketplaces.Api.Clients;

public class YandexClient
{
    private readonly AppSettings _appSettings;
    private readonly ILogger<YandexClient> _logger;
    private const string GetNomenclaturesUrl = "businesses/{businessId}/offer-mappings";
    private const string StocksUrl = "campaigns/{campaignId}/offers/stocks";
    private const string GetDataUrl = "campaigns";
    private const string GetPriceUrl = "/campaigns/{campaignId}/offer-prices";
    private const string UpdatePriceUrl = "/businesses/{businessId}/offer-prices/updates";
    private const string GetOrdersUrl = "/campaigns/{campaignId}/orders";


    private const int Limit = 100;

    public YandexClient(IOptions<AppSettings> options, ILogger<YandexClient> logger)
    {
        _logger = logger;
        _appSettings = options.Value;
    }

    public async Task<YandexDataDto> GetData(IAuthenticationData authenticationData)
    {
        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var client = GetRestClient(yandexAuthenticationData);
        var request = new RestRequest(GetDataUrl);
        var response = await client.ExecuteAsync<YandexDataDto>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
            throw new ApiException("Не получилось загрузить yandex данные");
        }

        return response.Data;
    }

    private RestClient GetRestClient(YandexAuthenticationData yandexAuthenticationData)
    {
        var options = new RestClientOptions(_appSettings.YandexUrl);
        var client = new RestClient(options);
        client.AddDefaultHeader("Api-Key", yandexAuthenticationData.ApiKey);
        return client;
    }

    public async Task<List<YandexNomenclatureDto>> GetNomenclatures(IAuthenticationData authenticationData)
    {
        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            return [];
        }

        var results = new List<YandexNomenclatureDto>();
        var client = GetRestClient(yandexAuthenticationData);
        client.AddDefaultUrlSegment("businessId", yandexAuthenticationData.BusinessId.ToString());
        var request = new RestRequest(GetNomenclaturesUrl, Method.Post);

        var requestParams = new GetYandexNomenclaturesParams()
        {
            PageToken = null,
            Limit = Limit
        };

        request.AddParameter("limit", requestParams.Limit, ParameterType.QueryString);
        request.AddParameter("page_token", requestParams.PageToken ?? "", ParameterType.QueryString);

        var response = await client.ExecuteAsync<YandexNomenclatureDtoWrapper>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
            throw new ApiException("Не получилось получить номенклатуры из yandex");
        }

        foreach (var offerMapping in response.Data.Result.OfferMappings)
        {
            if (offerMapping.Offer.CardStatus == "NO_CARD_ADD_TO_CAMPAIGN")
            {
                continue;
            }

            results.Add(new YandexNomenclatureDto()
            {
                Id = Guid.NewGuid().ToString(),
                Name = offerMapping.Offer.Name,
                Code = offerMapping.Offer.OfferId,
                Sku = offerMapping.Mapping.marketSku,
                Image = offerMapping.Offer.Pictures[0],
                CommonCode = offerMapping.Mapping.marketModelId
            });
        }

        return results;
    }

    public async Task<List<YandexOfferStockDto>> GetFBSStocks(IAuthenticationData authenticationData, List<string> skus)
    {
        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var client = GetRestClient(yandexAuthenticationData);
        client.AddDefaultUrlSegment("campaignId", yandexAuthenticationData.CampaignId.ToString());


        var result = new List<YandexOfferStockDto>();
        var skusChunks = skus.Chunk(100);
        foreach (var skusChunk in skusChunks)
        {
            var request = new RestRequest(StocksUrl, Method.Post);
            var body = new
            {
                offerIds = skusChunk,
                withTurnover = true
            };

            request.AddBody(body);
            var response = await client.ExecuteAsync<YandexStocksDtoWrapper>(request);
            if (!response.IsSuccessful || response.Data == null)
            {
                _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
                throw new ApiException("Не получилось получить номенклатуры из yandex");
            }

            result.AddRange(response.Data.Result.Warehouses[0].Offers);
        }

        return result;
    }

    public Task UpdateStocks(IAuthenticationData? authenticationData, string code, string sku, int amount)
    {
        return UpdateStocks(authenticationData, [new UpdateStocksItem(code, sku, amount)]);
    }

    public async Task UpdateStocks(IAuthenticationData? authenticationData, List<UpdateStocksItem> items)
    {
        if (items.Count == 0)
        {
            return;
        }

        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var client = GetRestClient(yandexAuthenticationData);
        var request = new RestRequest(StocksUrl, Method.Put);
        client.AddDefaultUrlSegment("campaignId", yandexAuthenticationData.CampaignId.ToString());
        var body = new
        {
            Skus = items.Select(i => new
                {
                    Sku = i.Code,
                    items = new[]
                    {
                        new
                        {
                            count = i.FbsAmount
                        }
                    }
                }
            )
        };
        request.AddBody(body);
        _logger.LogInformation($"Записываем в yandex, {string.Join(' ', items)}");

        var response = await client.ExecuteAsync<YandexStocksDtoWrapper>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
            throw new ApiException("Не получилось обновить количество в yandex");
        }
    }

    public async Task<List<YandexPricesDto>> GetPrices(IAuthenticationData? authenticationData, List<string> skus)
    {
        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var client = GetRestClient(yandexAuthenticationData);
        client.AddDefaultUrlSegment("campaignId", yandexAuthenticationData.CampaignId.ToString());


        var result = new List<YandexPricesDto>();
        var skusChunks = skus.Chunk(100);
        foreach (var skusChunk in skusChunks)
        {
            var request = new RestRequest(GetPriceUrl, Method.Post);
            var body = new
            {
                offerIds = skusChunk
            };

            request.AddBody(body);
            var response = await client.ExecuteAsync<YandexPricesDtoWrapper>(request);
            if (!response.IsSuccessful || response.Data == null)
            {
                _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
                throw new ApiException("Не получилось получить номенклатуры из yandex");
            }

            result.AddRange(response.Data.Result.Offers);
        }

        return result;
    }

    public async Task UpdatePrice(IAuthenticationData? authenticationData,
        List<(string Code, decimal Price)> yandexPrices)
    {
        if (yandexPrices.Count == 0)
        {
            return;
        }

        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var client = GetRestClient(yandexAuthenticationData);
        var request = new RestRequest(UpdatePriceUrl, Method.Post);
        client.AddDefaultUrlSegment("businessId", yandexAuthenticationData.BusinessId.ToString());
        var body = new
        {
            offers = yandexPrices.Select(p =>
                new
                {
                    offerId = p.Code,
                    price = new
                    {
                        currencyId = "RUR",
                        value = p.Price
                    }
                })
        };
        request.AddBody(body);
        var response = await client.ExecuteAsync(request);
        if (!response.IsSuccessful)
        {
            _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
            throw new ApiException("Не получилось обновить цену в yandex");
        }
    }

    public async Task<List<YandexOfferDto>> GetOrders(IAuthenticationData? authenticationData)
    {
        if (authenticationData is not YandexAuthenticationData yandexAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }


        var client = GetRestClient(yandexAuthenticationData);
        var request = new RestRequest(GetOrdersUrl);
        client.AddDefaultUrlSegment("campaignId", yandexAuthenticationData.CampaignId.ToString());
        request.AddQueryParameter("fromDate", DateTime.Now.AddDays(-2).ToString("yyyy-MM-dd"));
        request.AddQueryParameter("toDate", DateTime.Now.AddDays(1).ToString("yyyy-MM-dd"));
        request.AddQueryParameter("limit", 100);
        var response = await client.ExecuteAsync<YandexOffersDto>(request);
        if (!response.IsSuccessful || response.Data == null)
        {
            _logger.LogError(response.ErrorException, "Error: message: {Message}", response.ErrorMessage);
            throw new ApiException("Не получилось получить заказы в yandex");
        }

        return response.Data.Orders;
    }
}

public class YandexOffersDto
{
    public List<YandexOfferDto> Orders { get; set; }
    public YandexFlippingPagerDto Paging { get; set; }
    public ForwardScrollingPagerDTO Pager { get; set; }
}

public class YandexOfferDto
{
    public long Id { get; set; }
    public List<OrderItemDto> Items { get; set; }
}

public class OrderItemDto
{
    public string OfferId { get; set; } = null!;
    public string OfferName { get; set; } = null!;
    public int Count { get; set; }
}

public class ForwardScrollingPagerDTO
{
    public string NextPageToken { get; set; }
}

public class YandexFlippingPagerDto
{
    public int CurrentPage { get; set; }
    public int From { get; set; }
    public int PageSize { get; set; }
    public int PagesCount { get; set; }
    public int To { get; set; }
    public int Total { get; set; }
}

public class YandexStocksDtoWrapper
{
    public YandexStocksResultDto Result { get; set; } = null!;
}

public class YandexStocksResultDto
{
    public List<YandexWerohouseStockDto> Warehouses { get; set; }
}

public class YandexWerohouseStockDto
{
    public int WarehouseId { get; set; }
    public List<YandexOfferStockDto> Offers { get; set; }
}

public class YandexOfferStockDto
{
    public string OfferId { get; set; }
    public List<WarehouseStockDto> Stocks { get; set; }
}

public class WarehouseStockDto
{
    public string Type { get; set; }
    public int Count { get; set; }
}

public class YandexDataDto
{
    public List<YandexCampaign> Campaigns { get; set; }
}

public class YandexCampaign
{
    public int Id { get; set; }
    public int ClientId { get; set; }
    public YandexBusiness Business { get; set; }
}

public class YandexBusiness
{
    public int Id { get; set; }
    public string Name { get; set; }
}

public class YandexNomenclatureDtoWrapper
{
    public string Status { get; set; } = null!;
    public YandexNomenclatureResultDto Result { get; set; }
}

public class YandexPricesDtoWrapper
{
    public YandexPricesListDto Result { get; set; } = null!;
}

public class YandexPricesListDto
{
    public List<YandexPricesDto> Offers { get; set; }

    // paging
    public YandexPagingDto Paging { get; set; }
}

public class YandexPricesDto
{
    public string OfferId { get; set; }
    public YandexPriceDto Price { get; set; }
}

public class YandexPriceDto
{
    public decimal Value { get; set; }
    public decimal DiscountBase { get; set; }
}

public class YandexNomenclatureResultDto
{
    public YandexPagingDto Paging { get; set; } = null!;
    public List<YandexMappingDto> OfferMappings { get; set; } = null!;
}

public class YandexMappingDto
{
    public OfferDto Offer { get; set; } = null!;
    public OfferMappingDto Mapping { get; set; }
}

public class OfferMappingDto
{
    public long marketSku { get; set; }
    public string marketSkuName { get; set; }
    public int marketModelId { get; set; }
    public string marketModelName { get; set; }
    public int marketCategoryId { get; set; }
    public string marketCategoryName { get; set; }
}

public class OfferDto
{
    public string OfferId { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public string VendorCode { get; set; } = null!;
    public string? CardStatus { get; set; }
    public List<string> Pictures { get; set; } = [];
}

public class YandexPagingDto
{
    public string? NextPageToken { get; set; }
}

public class GetYandexNomenclaturesParams
{
    public int Limit { get; set; }
    public string? PageToken { get; set; }
}

public class YandexAuthenticationData : IAuthenticationData
{
    public int BusinessId { get; set; }
    public int ClientId { get; set; }
    public int CampaignId { get; set; }
    public string ApiKey { get; set; } = null!;
}

public class YandexNomenclatureDto
{
    public string Id { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string Name { get; set; } = null!;
    public long Sku { get; set; }
    public string? Image { get; set; }
    public int? CommonCode { get; set; }
}

public static class SortingExtensions
{
    public static IList<Nomenclature> SortByWierdRules(this IList<Nomenclature> nomenclatures)
    {
        var result = new List<Nomenclature>();
        var groupedByName = nomenclatures.GroupBy(s => s.Name);

        foreach (var nameGroup in groupedByName)
        {
            // Обработка строк с wCode
            var withWCode = nameGroup.Where(s => !string.IsNullOrEmpty(s.Wildberries?.Code))
                .OrderBy(s => s.Wildberries?.Code)
                .ThenBy(s => s.Wildberries?.Size)
                .ToList();

            // Сбор oCode из строк с wCode
            var processedOCodes = withWCode
                .Where(s => s.Ozon?.CommonCode != null)
                .Select(s => s.Ozon?.CommonCode)
                .ToHashSet();

            // Разделение строк без wCode
            var withoutWCode = nameGroup.Where(s => string.IsNullOrEmpty(s.Wildberries?.Code)).ToList();
            var linkedOCodes = withoutWCode
                .Where(s => s.Ozon?.CommonCode != null && processedOCodes.Contains(s.Ozon?.CommonCode))
                .ToList();
            var remainingOCodes = withoutWCode.Except(linkedOCodes).ToList();

            // Обработка групп wCode и связанных oCode
            foreach (var wCode in withWCode.Select(s => s.Wildberries?.Code).Distinct().OrderBy(w => w))
            {
                var currentWCodeGroup = withWCode.Where(s => s.Wildberries?.Code == wCode).ToList();
                result.AddRange(currentWCodeGroup);

                // Добавление связанных oCode для текущего wCode
                var oCodesInGroup = currentWCodeGroup
                    .Where(s => s.Ozon?.CommonCode != null)
                    .Select(s => s.Ozon?.CommonCode)
                    .ToHashSet();

                var oCodeGroup = linkedOCodes
                    .Where(s => oCodesInGroup.Contains(s.Ozon?.CommonCode))
                    .OrderBy(s => s.Ozon?.Size)
                    .ToList();

                result.AddRange(oCodeGroup);
            }

            // Добавление оставшихся oCode (не связанных с wCode)
            var remainingGrouped = remainingOCodes
                .GroupBy(s => s.Ozon?.CommonCode)
                .OrderBy(g => g.Key)
                .SelectMany(g => g.OrderBy(s => s.Ozon?.Size))
                .ToList();

            result.AddRange(remainingGrouped);
        }

        return result;
    }
}