namespace Marketplaces.Api.Responses;

public class UserDto
{
    public string Email { get; set; } = null!;
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string? MiddleName { get; set; }
    public bool IsEmailConfirmed { get; set; }
    public bool OzonToken { get; set; }
    public bool WbToken { get; set; }
    public bool YandexToken { get; set; }
    public bool IsConsentGiven { get; set; }
    public bool IsSubscriptionEnabled { get; set; }
    public DateTimeOffset? SubscriptionEnd { get; set; }
}