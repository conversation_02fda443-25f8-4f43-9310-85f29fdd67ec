using Marketplaces.Api.Models;

namespace Marketplaces.Api.Requests;

public class UpdateInfoBody
{
    public decimal? BankTax { get; set; }
    public decimal? OzonTax { get; set; }
    public TaxMode OzonMode { get; set; }
    public decimal? WildberriesTax { get; set; }
    public TaxMode WildberriesMode { get; set; }
    public decimal? YandexTax { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public bool IsTrackingEnabled { get; set; }
}