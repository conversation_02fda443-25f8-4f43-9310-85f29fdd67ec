using Swashbuckle.AspNetCore.Filters;

namespace Marketplaces.Api.Requests.Examples;

public class UpdateAccountBodyExample : IExamplesProvider<UpdateAccountBody>
{
    public UpdateAccountBody GetExamples()
    {
        return new UpdateAccountBody()
        {
            Email = "<EMAIL>",
            FirstName = "Иван",
            LastName = "Иванов",
            MiddleName = "<PERSON><PERSON><PERSON><PERSON><PERSON>"
        };
    }
}