using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Route("payments")]
public class PaymentsController : ShopAbstractController
{
    private readonly YookassaClient _yookassaClient;

    public PaymentsController(DatabaseContext context, YookassaClient yookassaClient) : base(context)
    {
        _yookassaClient = yookassaClient;
    }

    [HttpPost]
    // [Authorize(Policy = Policies.EmailConfirmed)]
    [Authorize]
    public async Task<IActionResult> CreatePayment()
    {
        // here try to get not expired payment
        var shop = await GetShop();
        var obj = await Context.Set<CurrentPrice>().FirstAsync();
        var payment = new Payment(shop.Id, obj.Price);
        await Context.AddAsync(payment);
        await Context.SaveChangesAsync();

        var user = shop.GetCurrentUser();
        var paymentDto = await _yookassaClient.CreatePaymentAsync(user.Email!, user.Id, payment.Id.ToString(), obj.Price);
        payment.Update(paymentDto.Id, paymentDto.ExpiresAt);
        await Context.SaveChangesAsync();
        return Ok(new { paymentDto.Confirmation.ConfirmationUrl});
    }
}