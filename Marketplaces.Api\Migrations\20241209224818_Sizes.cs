﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class Sizes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON>",
                table: "YandexNomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON>",
                table: "WildberriesNomenclature",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON>",
                table: "OzonNomenclature",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "YandexNomenclature");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "OzonNomenclature");
        }
    }
}
