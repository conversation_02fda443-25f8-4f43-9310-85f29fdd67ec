using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Wildberries;

public class WbReportItemtDto
{
    [JsonPropertyName("nm_id")]
    public int Id { get; set; }

    [JsonPropertyName("sa_name")]
    public string Code { get; set; }

    [JsonPropertyName("ts_name")]
    public string Size { get; set; }

    [JsonPropertyName("quantity")]
    public int Quantity { get; set; }

    [JsonPropertyName("retail_price")]
    public decimal RetailPrice { get; set; }

    [JsonPropertyName("retail_amount")]
    public decimal RetailAmount { get; set; }

    [JsonPropertyName("commission_percent")]
    public decimal CommissionPercent { get; set; }

    [JsonPropertyName("delivery_amount")]
    public int DeliveryAmount { get; set; }

    [JsonPropertyName("return_amount")]
    public int ReturnAmount { get; set; }

    [JsonPropertyName("delivery_rub")]
    public decimal DeliveryPrice { get; set; }

    [JsonPropertyName("ppvz_for_pay")]
    public decimal ToSendToSeller { get; set; }

    [Json<PERSON>ropertyName("ppvz_vw")]
    public decimal Reward { get; set; }

    [JsonPropertyName("penalty")]
    public decimal Penalties { get; set; }

    [JsonPropertyName("storage_fee")]
    public decimal StorageFee { get; set; }

    [JsonPropertyName("acceptance")]
    public decimal Acceptance { get; set; }

    [JsonPropertyName("deduction")]
    public decimal Deduction { get; set; }

    public string Barcode { get; set; } = null!;
}