namespace Marketplaces.Api.Requests.Wildberries;

public class GetWbNomenclaturesBody
{
    public InternalSettings Settings { get; set; } = new();
}

public class InternalSettings
{
    public InternalFilter Filter { get; set; } = new();
    public InternalCursor Cursor { get; set; } = new();
}

public class InternalCursor
{
    public int Limit { get; set; } = 100;
    public int? NmID { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class InternalFilter
{
    public int WithPhoto { get; set; } = -1;
}