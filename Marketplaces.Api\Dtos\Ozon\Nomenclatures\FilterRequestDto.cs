using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Ozon.Nomenclatures;

public class FilterRequestDto
{
    [JsonPropertyName("offer_id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<string>? OfferIds { get; set; }
    [JsonPropertyName("product_id")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<string>? ProductIds { get; set; }
}

public class FilterWithLimitRequestDto
{
    public FilterWithLimitRequestDto(FilterRequestDto filter, int limit)
    {
        Filter = filter;
        Limit = limit;
    }

    public FilterRequestDto Filter { get; set; } 
    public int Limit { get; set; }
}