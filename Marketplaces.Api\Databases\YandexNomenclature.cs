namespace Marketplaces.Api.Databases;

public class YandexNomenclature : MarketplaceNomenclature
{
    public YandexNomenclature()
    {
    }

    public YandexNomenclature(string id, string name, string code, string sku, string? image, int? commonCode)
    {
        Id = id;
        Name = name;
        Code = code;
        SKU = sku;
        Image = image;
        CommonCode = commonCode;
    }

    public string Name { get; set; } = null!;
    public string Id { get; set; }
    public string Code { get; set; }
    public string SKU { get; set; }
    public Guid InternalId { get; set; }
    public int? FBSAmount { get; set; }
    public string? Image { get; set; }
    public decimal? Price { get; set; }
    public int? CommonCode { get; set; }

    public void UpdateImage(string? image)
    {
        Image = image;
    }

    public void UpdateFbs(int amount)
    {
        FBSAmount = amount;
    }

    public void SetPrice(decimal price)
    {
        Price = price;
    }

    public override void UpdateTitle(string name)
    {
        Name = name;
    }
}