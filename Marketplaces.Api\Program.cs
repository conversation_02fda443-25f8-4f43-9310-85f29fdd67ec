using System.Security.Claims;
using System.Text;
using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Dtos.Wildberries;
using Marketplaces.Api.Models;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Requests.Examples;
using Marketplaces.Api.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.Filters;

namespace Marketplaces.Api;

public static class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        ConfigureServices(builder.Services, builder.Configuration);
        var app = builder.Build();
        ConfigureHttpRequestPipeline(app);
        app.Run();
    }

    private static void ConfigureServices(IServiceCollection services, ConfigurationManager configuration)
    {
        services.AddControllers(e => e.Filters.Add<ApiExceptionFilter>());
        services.AddCors(options =>
        {
            options.AddDefaultPolicy(
                builder =>
                {
                    builder
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowAnyOrigin();
                });
        });

        services.AddOptions<AppSettings>()
            .Configure(configuration.Bind)
            .ValidateDataAnnotations()
            .ValidateOnStart();


        services.AddTransient<IEmailSender, EmailSender>();
        var config = configuration.Get<AppSettings>()!;
        var key = Encoding.UTF8.GetBytes(config.TokenSecret);

        services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = false,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key)
                };

                options.Events = new JwtBearerEvents
                {
                    OnTokenValidated = context =>
                    {
                        var userId = context.Principal?.FindFirstValue(ClaimTypes.NameIdentifier);
                        if (userId == null)
                        {
                            context.Fail("Invalid token.");
                            return Task.CompletedTask;
                        }

                        var securityStampFromToken = context.Principal?.FindFirstValue("ss");
                        var userManager = context.HttpContext.RequestServices.GetRequiredService<UserManager<ApplicationUser>>();
                        var user = userManager.FindByIdAsync(userId).GetAwaiter().GetResult();
                        if (user?.SecurityStamp != securityStampFromToken)
                        {
                            context.Fail("Invalid token.");
                        }

                        return Task.CompletedTask;
                    }
                };
            });

        services.AddAuthorization(options =>
        {
            options.AddPolicy(Policies.EmailConfirmed, policy =>
                policy.Requirements.Add(new EmailConfirmedRequirement()));
            options.AddPolicy(Policies.ActiveSubscription, policy =>
            {
                policy.Requirements.Add(new ActiveSubscriptionRequirement());
                // policy.Requirements.Add(new EmailConfirmedRequirement());
            });
        });
        services.AddScoped<IAuthorizationHandler, EmailConfirmedHandler>();
        services.AddScoped<IAuthorizationHandler, ActiveSubscriptionHandler>();
        services.AddHttpContextAccessor();

        services.AddSwaggerGen(setup =>
        {
            setup.ExampleFilters();
            setup.MapType<Marketplace>(() => new OpenApiSchema
            {
                Type = "string",
                Enum = Enum.GetNames(typeof(Marketplace))
                    .Select(name => (IOpenApiAny)new OpenApiString(name))
                    .ToList()
            });
            var jwtSecurityScheme = new OpenApiSecurityScheme
            {
                BearerFormat = "JWT",
                Name = "JWT Authentication",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.Http,
                Scheme = JwtBearerDefaults.AuthenticationScheme,
                Description = "Put **_ONLY_** your JWT Bearer token on textbox below!",
                Reference = new OpenApiReference
                {
                    Id = JwtBearerDefaults.AuthenticationScheme,
                    Type = ReferenceType.SecurityScheme
                }
            };

            setup.AddSecurityDefinition(jwtSecurityScheme.Reference.Id, jwtSecurityScheme);
            setup.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                { jwtSecurityScheme, Array.Empty<string>() },
            });
        });
        services.AddSwaggerExamplesFromAssemblyOf<UpdateInfoBodyExample>();
        services.AddSwaggerExamplesFromAssemblyOf<UpdateAccountBodyExample>();


        services.AddAutoMapper(typeof(MappingProfile));
        var connectionString = configuration.GetConnectionString("Postgres");

        services.AddDbContext<DatabaseContext>(options =>
            options.UseNpgsql(connectionString).EnableSensitiveDataLogging());

        services.AddScoped<IUserClaimsPrincipalFactory<ApplicationUser>, CustomClaimsPrincipalFactory>();
        services.AddIdentityCore<ApplicationUser>(options =>
            {
                // Здесь можно настраивать параметры Identity
                options.Password.RequireDigit = false;
                options.Password.RequiredLength = 6;
                options.Tokens.EmailConfirmationTokenProvider = TokenOptions.DefaultEmailProvider;
            })
            .AddEntityFrameworkStores<DatabaseContext>()
            .AddDefaultTokenProviders();

        services.Configure<IdentityOptions>(options =>
        {
            options.Password.RequireDigit = true;
            options.Password.RequiredLength = 8;
            options.Lockout.MaxFailedAccessAttempts = 5;
        });


        services.Configure<ForwardedHeadersOptions>(options => { options.ForwardedHeaders = ForwardedHeaders.All; });
        services.AddHttpContextAccessor();
        services.AddTransient<WildberriesClient>();
        services.AddTransient<OzonClient>();
        services.AddTransient<YandexClient>();
        services.AddTransient<StocksService>();
        services.AddTransient<YookassaClient>();
        services.AddTransient<PaymentsService>();
        services.AddTransient<DiscountCalculationService>();

        services.AddHostedService<StocksTracker>();
        services.AddHostedService<SubscriptionsTracker>();
        services.AddMemoryCache();
    }

    private static void ConfigureHttpRequestPipeline(IApplicationBuilder app)
    {
        var mapper = app.ApplicationServices.GetRequiredService<IMapper>();
        mapper.ConfigurationProvider.AssertConfigurationIsValid();
        app.UsePathBase("/api");
        using (var serviceScope = app.ApplicationServices.GetRequiredService<IServiceScopeFactory>().CreateScope())
        {
            var dbContext = serviceScope.ServiceProvider.GetRequiredService<DatabaseContext>();
            dbContext.Database.Migrate();
        }

        app.UseSwagger(options =>
        {
            //Workaround to use the Swagger UI "Try Out" functionality when deployed behind a reverse proxy (APIM) with API prefix /sub context configured
            options.PreSerializeFilters.Add((swagger, httpReq) =>
            {
                // Используем хост и порт из текущего запроса
                var serverUrl = $"{httpReq.Scheme}://{httpReq.Host}/api";

                swagger.Servers = new List<OpenApiServer>
                {
                    new OpenApiServer { Url = serverUrl }
                };
            });
        });
        app.UseSwaggerUI(options =>
        {
            options.SwaggerEndpoint("swagger/v1/swagger.json", "v1");
            options.RoutePrefix = string.Empty;
            options.ConfigObject.AdditionalItems.Add("persistAuthorization", "true");
        });
        app.UseRouting();
        app.UseCors();
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
    }
}

internal class SubscriptionsTracker : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;

    public SubscriptionsTracker(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
        var paymentsService = scope.ServiceProvider.GetRequiredService<PaymentsService>();

        while (!stoppingToken.IsCancellationRequested)
        {
            var unprocessedPayments = await context.Set<Payment>()
                .Where(p => p.CanceledAt == null && p.ConfirmedAt == null && p.ExternalId != null)
                .Select(p => p.ExternalId!.Value)
                .ToListAsync(stoppingToken);

            await paymentsService.ProcessPayments(unprocessedPayments);
            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}

internal class StocksTracker : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<StocksTracker> _logger;

    public StocksTracker(IServiceProvider serviceProvider, ILogger<StocksTracker> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var env = scope.ServiceProvider.GetRequiredService<IOptions<AppSettings>>();
        if (!env.Value.IsTrackingEnabled)
        {
            return;
        }

        var context = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
        var ozonClient = scope.ServiceProvider.GetRequiredService<OzonClient>();
        var wbClient = scope.ServiceProvider.GetRequiredService<WildberriesClient>();
        var memoryCache = scope.ServiceProvider.GetRequiredService<IMemoryCache>();
        var yandexClient = scope.ServiceProvider.GetRequiredService<YandexClient>();
        var stocksService = scope.ServiceProvider.GetRequiredService<StocksService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<StocksTracker>>();

        while (!stoppingToken.IsCancellationRequested)
        {
            var shops = await context.Shops
                .Include(s => s.ShopUsers)
                .ThenInclude(s => s.User)
                .Where(s => s.IsTrackingEnabled)
                .ToListAsync(stoppingToken);

            foreach (var shop in shops)
            {
                await stocksService.SyncStocks(shop.ShopUsers.First().UserId);
                var ozonKey = $"{shop.Id}:ozon:posts";
                var wbKey = $"{shop.Id}:wb:posts";
                var yandexKey = $"{shop.Id}:yandex:posts";

                List<PostingInfo>? ozonList = null;
                HashSet<string>? ozonCache = null;
                HashSet<PostingInfo>? ozonNewItems = null;

                List<WbOrderDto>? wbList = null;
                HashSet<string>? wbCache = null;
                HashSet<WbOrderDto>? wbNewItems = null;

                List<YandexOfferDto>? yandexList = null;
                HashSet<long>? yandexCache = null;
                HashSet<YandexOfferDto>? yandexNewItems = null;

                var isFirstAttempt = new IsFirstAttempt();
                try
                {
                    if (shop.ContainsToken(Marketplace.Ozon))
                    {
                        ozonList = await ozonClient.GetPostingList(shop);
                        if (memoryCache.TryGetValue(ozonKey, out var ozonTemp) &&
                            ozonTemp is HashSet<string> hashSet)
                        {
                            ozonCache = hashSet;
                            isFirstAttempt.Ozon = false;
                            ozonNewItems ??= [];
                            foreach (var info in ozonList.Where(info => !ozonCache.Contains(info.OrderNumber)))
                            {
                                _logger.LogInformation(
                                    "ozon. order number: {OrderNumber} was bought. Codes: {Codes}",
                                    info.OrderNumber,
                                    string.Join(", ", info.Products.Select(i => i.Name)));
                                ozonNewItems.Add(info);
                            }
                        }
                    }

                    if (shop.GetToken(Marketplace.Wildberries) is WbAuthenticationData wbAuthenticationData)
                    {
                        wbList = await wbClient.GetOrders(wbAuthenticationData);
                        if (memoryCache.TryGetValue(wbKey, out var wbTemp) && wbTemp is HashSet<string> hashSet)
                        {
                            wbCache = hashSet;
                            isFirstAttempt.Wb = false;
                            wbNewItems ??= [];
                            foreach (var info in wbList.Where(info => !wbCache.Contains(info.OrderUid)))
                            {
                                _logger.LogInformation("wb. order number: {OrderNumber} was bought. Codes: {Code}",
                                    info.OrderUid, info.NmId);
                                wbNewItems.Add(info);
                            }
                        }
                    }

                    if (shop.GetToken(Marketplace.Yandex) is YandexAuthenticationData yandexAuthenticationData)
                    {
                        yandexList = await yandexClient.GetOrders(yandexAuthenticationData);
                        if (memoryCache.TryGetValue(yandexKey, out var yandexTemp) &&
                            yandexTemp is HashSet<long> hashSet)
                        {
                            yandexCache = hashSet;
                            isFirstAttempt.Yandex = false;
                            yandexNewItems ??= [];
                            foreach (var info in yandexList.Where(info => !yandexCache.Contains(info.Id)))
                            {
                                _logger.LogInformation(
                                    "yandex. order number: {OrderNumber} was bought. Codes: {Code}. Numbers: {numbers}",
                                    info.Id, info.Items.Select(i => i.OfferId), info.Items.Select(i => i.Count));
                                yandexNewItems.Add(info);
                            }
                        }
                    }

                    // in current place we know the exact new items grouped by marketplaces
                    // so we need to aggregate them 

                    var nomenclatures = await context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
                        .Include(n => n.Ozon)
                        .Include(n => n.Wildberries)
                        .Include(n => n.Yandex)
                        .ToListAsync(stoppingToken);

                    var itemsToUpdate = AggregateNewItems(nomenclatures, ozonNewItems, wbNewItems, yandexNewItems);


                    var filteredNomenclatures = nomenclatures.Where(n => itemsToUpdate.Contains(n.Id)).ToList();
                    // for ozon part
                    if (shop.ContainsToken(Marketplace.Ozon))
                    {
                        var ozonWarehouses = await ozonClient.GetWarehouseIds(shop);
                        var ozonNomenclatures = filteredNomenclatures
                            .Where(n => n.Ozon is { FBSAmount: not null })
                            .Select(n => new UpdateStocksItem(n.Ozon!.Code, n.Ozon.SKU, n.Ozon.FBSAmount!.Value))
                            .ToList();

                        await ozonClient.UpdateStocks(shop, ozonWarehouses[0], ozonNomenclatures);
                    }

                    // for wb part
                    var wbToken = shop.GetToken(Marketplace.Wildberries);
                    if (wbToken != null)
                    {
                        var warehouses = await wbClient.GetWarehouses(wbToken);

                        var wbNomenclatures = filteredNomenclatures
                            .Where(n => n.Wildberries is { FBSAmount: not null })
                            .Select(n =>
                                new UpdateStocksItem(n.Wildberries!.Code, n.Wildberries!.SKU,
                                    n.Wildberries.FBSAmount!.Value))
                            .ToList();

                        var one = warehouses.FirstOrDefault(w => w.DeliveryType == 1);
                        if (one != null)
                        {
                            await wbClient.UpdateStocks(wbToken, one.Id, wbNomenclatures);
                        }
                    }

                    var yandexToken = shop.GetToken(Marketplace.Yandex);
                    if (yandexToken != null)
                    {
                        var yandexNomenclatures = filteredNomenclatures
                            .Where(n => n.Yandex is { FBSAmount: not null })
                            .Select(n =>
                                new UpdateStocksItem(n.Yandex!.Code, n.Yandex.SKU, n.Yandex.FBSAmount!.Value))
                            .ToList();
                        await yandexClient.UpdateStocks(yandexToken, yandexNomenclatures);
                    }

                    await context.SaveChangesAsync(stoppingToken);

                    var ozonFetchedSet = ozonList?.Select(s => s.OrderNumber).ToHashSet() ?? [];
                    ozonCache?.UnionWith(ozonFetchedSet);
                    ozonCache ??= ozonFetchedSet;
                    memoryCache.Set(ozonKey, ozonCache, TimeSpan.FromDays(2));

                    var wbFetchedSet = wbList?.Select(s => s.OrderUid).ToHashSet() ?? [];
                    wbCache?.UnionWith(wbFetchedSet);
                    wbCache ??= wbFetchedSet;
                    memoryCache.Set(wbKey, wbCache, TimeSpan.FromDays(2));

                    var yandexFetchedSet = yandexList?.Select(s => s.Id).ToHashSet() ?? [];
                    yandexCache?.UnionWith(yandexFetchedSet);
                    yandexCache ??= yandexFetchedSet;
                    memoryCache.Set(yandexKey, yandexCache, TimeSpan.FromDays(2));
                }
                catch (Exception e)
                {
                    logger.LogWarning(e, "Exception in background service");
                }
            }

            await Task.Delay(TimeSpan.FromMinutes(2), stoppingToken);
        }
    }

    private HashSet<int> AggregateNewItems(List<Nomenclature> nomenclatures,
        HashSet<PostingInfo>? ozonNewItems,
        HashSet<WbOrderDto>? wbNewItems,
        HashSet<YandexOfferDto>? yandexNewItems)
    {
        var hashset = new HashSet<int>();
        foreach (var ozonNewItem in ozonNewItems ?? [])
        {
            foreach (var product in ozonNewItem.Products)
            {
                var n = nomenclatures.Find(n => n.Ozon?.Code == product.OfferId);
                if (n != null)
                {
                    hashset.Add(n.Id);
                    n.ReduceFbsForAllMarketplacesExcept(Marketplace.Ozon, product.Quantity);
                }
            }
        }

        foreach (var wbNewItem in wbNewItems ?? [])
        {
            var n = nomenclatures.Find(n => n.Wildberries?.Id == wbNewItem.NmId);
            if (n != null)
            {
                hashset.Add(n.Id);
                n.ReduceFbsForAllMarketplacesExcept(Marketplace.Wildberries, 1);
            }
        }

        foreach (var yandexNewItem in yandexNewItems ?? [])
        {
            foreach (var item in yandexNewItem.Items)
            {
                var n = nomenclatures.Find(n => n.Yandex?.Code == item.OfferId);
                if (n != null)
                {
                    hashset.Add(n.Id);
                    n.ReduceFbsForAllMarketplacesExcept(Marketplace.Yandex, item.Count);
                }
            }
        }

        return hashset;
    }
}

public class IsFirstAttempt
{
    public IsFirstAttempt()
    {
        Ozon = true;
        Wb = true;
        Yandex = true;
    }

    public bool Ozon { get; set; }
    public bool Wb { get; set; }
    public bool Yandex { get; set; }
}