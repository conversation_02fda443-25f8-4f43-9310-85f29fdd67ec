﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class MoreOzonPrices : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "MarketingPrice",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MarketingSellerPrice",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "<PERSON><PERSON><PERSON>",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "OldPrice",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MarketingPrice",
                table: "OzonNomenclature");

            migrationBuilder.DropColumn(
                name: "MarketingSellerPrice",
                table: "OzonNomenclature");

            migrationBuilder.DropColumn(
                name: "MinPrice",
                table: "OzonNomenclature");

            migrationBuilder.DropColumn(
                name: "OldPrice",
                table: "OzonNomenclature");
        }
    }
}
