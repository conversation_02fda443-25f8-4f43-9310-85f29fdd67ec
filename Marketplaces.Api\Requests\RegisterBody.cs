using System.ComponentModel.DataAnnotations;

namespace Marketplaces.Api.Requests;

public class RegisterBody
{
    [Required, EmailAddress] public string Email { get; set; } = null!;
    [Required] public string Password { get; set; } = null!;
    [Required] public string FirstName { get; set; } = null!;
    [Required] public string LastName { get; set; } = null!;
    public string? MiddleName { get; set; }
    // [Phone]
    // public string? PhoneNumber { get; set; }
    public string? ShopName { get; set; }
    [Required]
    public bool IsConsentGiven { get; set; }
    public string? PromoCode { get; set; }
}