using Marketplaces.Api.Clients;
using Marketplaces.Api.Dtos.Ozon.Promotes;

namespace Marketplaces.Api.Dtos.Internal;

public class OzonPriceInfoDto
{
    public decimal Price { get; set; }
    public decimal Acquiring { get; set; }
    public decimal SalesPercent { get; set; }
    public decimal SalesPercentFbo { get; set; }
    public decimal SalesPercentFbs { get; set; }
    public decimal FboFulfillmentAmount { get; set; }
    public decimal FboDirectFlowTransMinAmount { get; set; }
    public decimal FboDirectFlowTransMaxAmount { get; set; }
    public decimal FboDelivToCustomerAmount { get; set; }
    public decimal FboReturnFlowAmount { get; set; }
    public decimal FboReturnFlowTransMinAmount { get; set; }
    public decimal FboReturnFlowTransMaxAmount { get; set; }
    public decimal FbsFirstMileMinAmount { get; set; }
    public decimal FbsFirstMileMaxAmount { get; set; }
    public decimal FbsDirectFlowTransMinAmount { get; set; }
    public decimal FbsDirectFlowTransMaxAmount { get; set; }
    public decimal FbsDelivToCustomerAmount { get; set; }
    public decimal FbsReturnFlowAmount { get; set; }
    public decimal FbsReturnFlowTransMinAmount { get; set; }
    public decimal FbsReturnFlowTransMaxAmount { get; set; }
    public int? RedemptionAmount { get; set; }
    public int? ReturnAmount { get; set; }
}

public class PromoteDto
{
    public int Id { get; set; }
    public string Title { get; set; } = null!;
    public decimal Value { get; set; }
    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
}