on:
  push:
    branches:
      - master
env:
  DOCKER_BUILDKIT: 1
jobs:
  publish:
    name: Publish
    runs-on: [external]
    container: docker:stable
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Build container
        run: >
          docker build
          -t marketplaces-image:latest
          --target runtime
          .
      - name: Remove existing container
        run: |
          docker container stop marketplaces || true
          docker container rm marketplaces || true
      - name: Run app
        run: >
          docker run -d --restart always --name marketplaces -p 8080:8080/tcp
          -e ConnectionStrings__Postgres="${{ secrets.POSTGRES_MARKETPLACES }}"
          -e TokenSecret="${{ secrets.TOKEN_SECRET }}"
          -e EmailPassword="${{ secrets.EMAIL_PASSWORD }}"
          -e YookassaShopId="${{ secrets.YOOKASSA_SHOP_ID }}"
          -e YookassaSecretKey="${{ secrets.YOOKASSA_SECRET_KEY }}"
          -e TZ="Europe/Moscow"
          -e ASPNETCORE_ENVIRONMENT="Production"
          marketplaces-image:latest
      - name: Prune images
        run: docker image prune -a -f
