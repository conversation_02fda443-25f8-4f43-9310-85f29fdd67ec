namespace Marketplaces.Api.Responses;

public class PriceDto
{
    public int Id { get; set; }
    public int? MinAmount { get; set; }
    public string? Name { get; set; }
    public string? Image { get; set; }
    public NestedPriceDto? Ozon { get; set; }
    public NestedPriceDto? Yandex { get; set; }
    public NestedPriceDto? Wildberries { get; set; }
    public decimal? PurchasePrice { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public string? Size { get; set; }
}