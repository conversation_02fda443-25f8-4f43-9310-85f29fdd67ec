﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    /// <inheritdoc />
    public partial class Prices : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Price",
                table: "YandexNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Discount",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "<PERSON>",
                table: "WildberriesNomenclature",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Price",
                table: "OzonNomenclature",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "YandexNomenclature");

            migrationBuilder.DropColumn(
                name: "Discount",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "Price",
                table: "WildberriesNomenclature");

            migrationBuilder.DropColumn(
                name: "Price",
                table: "OzonNomenclature");
        }
    }
}
