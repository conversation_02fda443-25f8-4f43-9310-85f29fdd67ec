﻿FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

WORKDIR /source
COPY nuget.config .
COPY *.sln .
# copy csproj and restore as distinct layers
COPY Marketplaces.Api/Marketplaces.Api.csproj Marketplaces.Api/
# Add additional projects here
RUN dotnet restore -p:PublishReadyToRun=true Marketplaces.Api/Marketplaces.Api.csproj

# copy and build app
COPY Marketplaces.Api/ ./Marketplaces.Api/
# Add additional projects here
WORKDIR /source/Marketplaces.Api
RUN dotnet build -c release --no-restore
RUN dotnet publish -c release --no-build -o /app

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app
COPY --from=build /app .
ENTRYPOINT ["dotnet", "Marketplaces.Api.dll"]
