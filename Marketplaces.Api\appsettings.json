{"Logging": {"Console": {"LogLevel": {"Default": "Warning", "Marketplaces.Api": "Information"}, "FormatterName": "simple", "FormatterOptions": {"SingleLine": false, "IncludeScopes": false, "TimestampFormat": "dd-MM-yy HH:mm:ss ", "UseUtcTimestamp": false, "JsonWriterOptions": {"Indented": false}}}}, "TokenSecret": "camksauwrt1ium31xaASDfmfj1wxzarbi", "YookassaReturnUrl": "/shops-settings/user", "TokenLifetimeInDays": 365}