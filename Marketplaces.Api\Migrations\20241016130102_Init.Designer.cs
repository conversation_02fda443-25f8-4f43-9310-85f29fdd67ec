﻿// <auto-generated />
using System;
using Marketplaces.Api.Databases;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Marketplaces.Api.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20241016130102_Init")]
    partial class Init
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Marketplaces.Api.Databases.Nomenclature", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OzonCode")
                        .HasColumnType("text");

                    b.Property<int?>("OzonFbo")
                        .HasColumnType("integer");

                    b.Property<int?>("OzonFbs")
                        .HasColumnType("integer");

                    b.Property<int?>("OzonId")
                        .HasColumnType("integer");

                    b.Property<string>("OzonSKU")
                        .HasColumnType("text");

                    b.Property<int>("ShopId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Updated")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("WildberriesCode")
                        .HasColumnType("text");

                    b.Property<int?>("WildberriesFbo")
                        .HasColumnType("integer");

                    b.Property<int?>("WildberriesFbs")
                        .HasColumnType("integer");

                    b.Property<int?>("WildberriesId")
                        .HasColumnType("integer");

                    b.Property<string>("WildberriesSKU")
                        .HasColumnType("text");

                    b.Property<string>("YandexCode")
                        .HasColumnType("text");

                    b.Property<int?>("YandexFbo")
                        .HasColumnType("integer");

                    b.Property<int?>("YandexFbs")
                        .HasColumnType("integer");

                    b.Property<string>("YandexId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("YandexSKU")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ShopId");

                    b.ToTable("Nomenclature", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Models.Shop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OzonClientId")
                        .HasColumnType("text");

                    b.Property<string>("OzonKey")
                        .HasColumnType("text");

                    b.Property<string>("WbKey")
                        .HasColumnType("text");

                    b.Property<string>("YandexClientId")
                        .HasColumnType("text");

                    b.Property<string>("YandexKey")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Shop", (string)null);
                });

            modelBuilder.Entity("Marketplaces.Api.Databases.Nomenclature", b =>
                {
                    b.HasOne("Marketplaces.Api.Models.Shop", null)
                        .WithMany()
                        .HasForeignKey("ShopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
