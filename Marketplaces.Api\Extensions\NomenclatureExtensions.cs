using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Marketplaces.Api.Responses;

namespace Marketplaces.Api.Extensions;

public static class NomenclatureExtensions
{
    public static void CalculateProfitFields(this PriceDto priceDto, Nomenclature nomenclature, Shop shop)
    {
        if (priceDto.Ozon != null && nomenclature.Ozon != null)
        {
            var ozonTax = GetMarketplaceTax(shop.OzonMode, shop.OzonTax, shop.OzonEstimatedTax);
            CalculateNestedProfitFields(priceDto.Ozon, nomenclature.PurchasePrice, shop.BankTax, ozonTax);
        }

        if (priceDto.Wildberries != null && nomenclature.Wildberries != null)
        {
            var wbTax = GetMarketplaceTax(shop.WildberriesMode, shop.WildberriesTax, shop.WildberriesEstimatedTax);
            CalculateNestedProfitFields(priceDto.Wildberries, nomenclature.PurchasePrice, shop.BankTax, wbTax);
        }

        if (priceDto.Yandex != null && nomenclature.Yandex != null)
        {
            // Для Yandex пока нет расчетной комиссии, используем только ручную
            CalculateNestedProfitFields(priceDto.Yandex, nomenclature.PurchasePrice, shop.BankTax, shop.YandexTax);
        }
    }

    private static void CalculateNestedProfitFields(NestedPriceDto nestedDto, decimal? purchasePrice, decimal? bankTax, decimal? marketplaceTax)
    {
        if (nestedDto.BasePrice == null || purchasePrice == null || bankTax == null || marketplaceTax == null)
            return;

        var salePrice = nestedDto.BasePrice.Value;
        if (nestedDto.DiscountPercent.HasValue)
        {
            var discountMultiplier = 1 - (nestedDto.DiscountPercent.Value / 100);
            salePrice = nestedDto.BasePrice.Value * discountMultiplier;
        }

        var bankCommissionAmount = salePrice * (bankTax.Value / 100);

        var marketplaceCommissionAmount = salePrice * (marketplaceTax.Value / 100);

        var revenue = salePrice - bankCommissionAmount - marketplaceCommissionAmount;

        var profit = revenue - purchasePrice.Value;

        var profitPercentage = purchasePrice.Value != 0 ? profit / purchasePrice.Value * 100 : 0;

        nestedDto.SalePrice = Math.Round(salePrice, 2);
        nestedDto.BankCommissionAmount = Math.Round(bankCommissionAmount, 2);
        nestedDto.MarketplaceCommissionAmount = Math.Round(marketplaceCommissionAmount, 2);
        nestedDto.Revenue = Math.Round(revenue, 2);
        nestedDto.Profit = Math.Round(profit, 2);
        nestedDto.ProfitPercentage = Math.Round(profitPercentage, 2);
    }

    /// <summary>
    /// Получает комиссию маркетплейса в зависимости от режима (ручная или расчетная)
    /// </summary>
    /// <param name="mode">Режим комиссии (Manual или Estimated)</param>
    /// <param name="manualTax">Ручная комиссия</param>
    /// <param name="estimatedTax">Расчетная комиссия</param>
    /// <returns>Комиссия для использования в расчетах</returns>
    private static decimal? GetMarketplaceTax(TaxMode mode, decimal? manualTax, decimal? estimatedTax)
    {
        return mode switch
        {
            TaxMode.Manual => manualTax,
            TaxMode.Estimated => estimatedTax ?? manualTax, // Если расчетной нет, используем ручную как fallback
            _ => manualTax
        };
    }
}
