using System.Text.Json.Serialization;

namespace Marketplaces.Api.Dtos.Ozon.Nomenclatures;

public class NomenclatureInfoDto
{
    [JsonPropertyName("id")]
    public long Id { get; set; }

    [JsonPropertyName("offer_id")]
    public string OfferId { get; set; } = null!;

    [JsonPropertyName("primary_image")]
    public List<string> Images { get; set; } = [];
    public string Name { get; set; } = null!;
    public List<SourceItem> Sources { get; set; } = [];
    public string? Size { get; set; }
    public int? CommonCode { get; set; }
}

public class SourceItem
{
    public long Sku { get; set; }
    public string Source { get; set; } = null!;
    // public DateTime CreatedAt { get; set; }
    // public string ShipmentType { get; set; } = null!;
    // public string QuantCode { get; set; } = null!;
}

//     "sources" : [ {
//   "sku" : 964787997,
//   "source" : "sds",
//   "created_at" : "2023-05-03T15:08:54.188953Z",
//   "shipment_type" : "SHIPMENT_TYPE_GENERAL",
//   "quant_code" : ""
// }, {
//   "sku" : 964787996,
//   "source" : "fbs",
//   "created_at" : "2023-05-03T15:08:54.170613Z",
//   "shipment_type" : "SHIPMENT_TYPE_GENERAL",
//   "quant_code" : ""
// } ],