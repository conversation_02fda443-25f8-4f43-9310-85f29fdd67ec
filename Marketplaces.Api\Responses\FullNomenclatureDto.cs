namespace Marketplaces.Api.Responses;

public class FullNomenclatureDto
{
    public int Id { get; set; }
    public int? MinAmount { get; set; }
    public string? Name { get; set; }
    public string? Image { get; set; }
    public FullInnerNomenclatureDto? Ozon { get; set; }
    public FullInnerNomenclatureDto? Yandex { get; set; }
    public FullInnerNomenclatureDto? Wildberries { get; set; }
    public decimal? PurchasePrice { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public string? Size { get; set; }
}